package profile.service

import com.twitter.inject.Logging
import com.twitter.util.Future
import com.typesafe.config.Config
import profile.domain.request.ShieldDataRequest
import profile.exception.{ShieldRequiredException, ShieldVerifyFailedException}
import profile.service.Action.Action
import profile.util.EmailHelper
import scalaj.http.Http
import vn.vhm.common.domain.Implicits.async
import vn.vhm.common.domain.OptionImplicits.OptionImplicitAny
import vn.vhm.common.domain.profiling.Profiler
import vn.vhm.common.exception.UnsupportedException
import vn.vhm.common.util.{JsonHelper, JsonHelperCamelCase, PhoneUtils}

/**
 * <AUTHOR>
 */
trait ShieldService {

  def checkAppVersionSupported(shieldProtectData: ShieldDataRequest): Future[Unit]

  def protectRequest[T](shieldProtectData: ShieldDataRequest,
                        action: Action.Action,
                        key: String,
                        customerIdentity: CustomerIdentity,
                        metadata: Map[String, Object] = Map.empty[String, Object])(fn: => Future[T]): Future[T]
}

case class ShieldServiceImpl(shieldConfig: Config) extends ShieldService with Logging {

  private val clazz = getClass.getCanonicalName

  import vn.vhm.common.util.ZConfig.ImplicitConfig

  private val baseUrl = shieldConfig.getString("base_url")
  private val connectTimeout = shieldConfig.getInt("connect_timeout_ms", 30000)
  private val requestTimeout = shieldConfig.getInt("request_timeout_ms", 30000)
  private val shieldIsEnable = shieldConfig.getBoolean("enable", false)
  private val noAppCheckLatestBuildVersion = shieldConfig.getLong("no_app_check_latest_build_number", 0)
  private val protectApiUrl = baseUrl + "/internal/protect-api"

  private val messageForOldApp = Map(
    "vi" -> "Vui lòng cập nhật ứng dụng để tiếp tục đăng ký và sử dụng các tính năng mới.",
    "en" -> "Please update the app to continue registration and access new features."
  )

  override def checkAppVersionSupported(shieldProtectData: ShieldDataRequest): Future[Unit] = Profiler(s"$clazz.checkAppBuildNumberSupported") {
    if (shieldIsEnable) {
      // check for AppCheck support
      shieldProtectData.appVersion match {
        case Some(build) if build.toLong <= noAppCheckLatestBuildVersion =>
          warn(s"Old app version not support => Denied\trequest=${JsonHelper.toJson(shieldProtectData)}")
          Future.exception(UnsupportedException(messageForOldApp(shieldProtectData.lang)))
        case None =>
          warn(s"Missing app version => Denied\trequest=${JsonHelper.toJson(shieldProtectData)}")
          Future.exception(UnsupportedException(messageForOldApp(shieldProtectData.lang)))
        case _ => Future.Unit
      }
    } else Future.Unit
  }

  override def protectRequest[T](req: ShieldDataRequest,
                                 action: Action.Action,
                                 key: String,
                                 customerIdentity: CustomerIdentity,
                                 metadata: Map[String, Object] = Map.empty[String, Object])(fn: => Future[T]): Future[T] = Profiler(s"$clazz.protectRequest") {
    for {
      // Check build number support shield
      _ <- checkAppVersionSupported(req)

      _ <- if (shieldIsEnable) {
        for {
          protectResult <- checkAndProtect(req, action, key, customerIdentity, metadata)
          result <- protectResult match {
            case Some(result) if result.state == 0 && !result.evaluationResult.exists(_.pass) => async {
              val evaluationResult = result.evaluationResult.get
              warn(s"Shield required => Denied\trequest=${JsonHelper.toJson(req)}")
              throw ShieldRequiredException(
                evaluationResult.code,
                evaluationResult.message,
                evaluationResult.shieldToken,
                evaluationResult.holdTimeInSecond
              )
            }
            case Some(result) if result.state == 1 && !result.verifyResult.exists(_.pass) => async {
              val verifyResult = result.verifyResult.get
              warn(s"Shield verify failed => Denied\trequest=${JsonHelper.toJson(req)}\tverifyResult=${JsonHelper.toJson(verifyResult)}")
              throw ShieldVerifyFailedException(
                verifyResult.code,
                verifyResult.message
              )
            }
            case _ => Future.Unit
          }
        } yield result

      } else async {
        //        warn(s"Protect request by token is disabled => Bypassing verification for action ($action) and key ($key)")
      }

      result <- fn

    } yield result
  }

  /**
   * Check and protect the request using the provided data
   *
   * @param shieldProtectData The request data containing the token and other information
   * @param action            The action being performed
   * @param key               The key for verification
   * @param metadata          Additional metadata for verification
   * @return A future containing the verification result
   */
  private def checkAndProtect(shieldProtectData: ShieldDataRequest,
                           action: Action,
                           key: String,
                           customerIdentity: CustomerIdentity,
                           metadata: Map[String, Object] = Map.empty[String, Object]): Future[Option[ApiProtectResponse]] = Profiler("checkAndProtect") {
    async {
      val payload = ApiProtectRequest(
        action = action.toString,
        key = key,
        customerIdentity = customerIdentity,
        deviceFingerprint = DeviceFingerprint(deviceId = shieldProtectData.deviceId, deviceOs = shieldProtectData.deviceOs),
        requestId = shieldProtectData.requestId,
        protectTokens = shieldProtectData.protectTokens,
        shieldToken = shieldProtectData.shieldToken,
        ipAddress = shieldProtectData.ipAddress,
        userAgent = shieldProtectData.userAgent,
        metadata = Map.empty[String, Object]
      )

      try {
        val resp = Http(protectApiUrl)
          .timeout(connTimeoutMs = connectTimeout, readTimeoutMs = requestTimeout)
          .header("Content-Type", "application/json")
          .header("Accept-Language", shieldProtectData.lang)
          .postData(JsonHelperCamelCase.toJson(payload))
          .asString

        resp.code match {
          case 200 =>
            val result = JsonHelperCamelCase.fromJson[VClubShieldResp[ApiProtectResponse]](resp.body)
            result.data.toSome
          case 400 =>
            warn(s"Invalid request to shield service (${resp.code} - ${resp.body}) => Block request for action ($action) and key ($key)")
            throw UnsupportedException(messageForOldApp(shieldProtectData.lang))
          case _ =>
            warn(s"Failed to call shield service (${resp.code} - ${resp.body}) => Bypass protect for action ($action) and key ($key)")
            None
        }
      } catch {
        case e: UnsupportedException =>
          throw e
        case e: Exception =>
          error(s"Exception when protect api (${e.getMessage}) => Bypass protect for action ($action) and key ($key)", e)
          None
      }
    }
  }
}

case class VClubShieldResp[T](code: Int, message: Seq[String], data: T)

case class ApiProtectResponse(
                               state: Int, // 0: evaluation state, 1: verify state
                               evaluationResult: Option[ShieldEvaluationResult],
                               verifyResult: Option[ShieldVerifyResult]
                             )

case class ApiProtectRequest(
                              action: String,
                              key: String,
                              customerIdentity: CustomerIdentity,
                              deviceFingerprint: DeviceFingerprint,
                              requestId: Option[String],
                              ipAddress: Option[String],
                              userAgent: Option[String],
                              protectTokens: Option[Seq[ProtectTokenData]],
                              shieldToken: Option[String],
                              metadata: Map[String, Object]
                            )

case class CustomerIdentity(
                             customerId: Option[String] = None,
                             phone: Option[String] = None,
                             email: Option[String] = None
                           )

object CustomerIdentity {

  def fromIdentify(identify: String): CustomerIdentity = {
    if (identify.contains("@")) {
      CustomerIdentity(email = EmailHelper.normalize(identify).toSome)
    } else {
      CustomerIdentity(phone = PhoneUtils.normalizePhone(identify).orElse(identify.toSome))
    }
  }

  def fromUseId(userId: String): CustomerIdentity = {
    CustomerIdentity(customerId = userId.toSome)
  }

}

case class DeviceFingerprint(
                              deviceId: Option[String] = None,
                              deviceOs: Option[String] = None
                            )

case class ProtectTokenData(
                             key: String,
                             value: String
                           )

case class ShieldEvaluationResult(
                                   pass: Boolean,
                                   code: Int,
                                   message: String,
                                   shieldToken: String,
                                   holdTimeInSecond: Long
                                 )

case class ShieldVerifyResult(
                               pass: Boolean,
                               code: Int,
                               message: String
                             )


object Action extends Enumeration {
  type Action = Value

  val REGISTER_CHECK = Value("REGISTER_CHECK")
  val REGISTER_VERIFY = Value("REGISTER_VERIFY")
  val REGISTER_CONFIRM = Value("REGISTER_CONFIRM")
  val REGISTER_ONBOARDING = Value("REGISTER_ONBOARDING")

  val LOGIN = Value("LOGIN")

  val VBD_SDK_REQUEST_TOKEN = Value("VBD_SDK_REQUEST_TOKEN")
}